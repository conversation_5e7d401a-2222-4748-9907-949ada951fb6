import { LANGUAGE } from "@enums/language";
import enTranslations from "./locales/en.json";
import thTranslations from "./locales/th.json";
import type { Resource } from "i18next";
import i18n from "i18next";
import { initReactI18next } from "react-i18next";

const resources: Resource = {
  en: {
    translation: enTranslations,
  },
  th: {
    translation: thTranslations,
  },
};

i18n.use(initReactI18next).init({
  fallbackLng: LANGUAGE.EN,

  interpolation: {
    escapeValue: false,
  },

  // debug: process.env.NODE_ENV === 'development',

  // Key separator
  keySeparator: ".",
  lng: LANGUAGE.EN,

  // Namespace separator
  nsSeparator: ":",
  resources,
});

export default i18n;
